/************************************************************/
/*    NAME: zhaoqinchao                                     */
/*    ORGN: HEU                                             */
/*    FILE: BlueSocket.h                                    */
/*    DATE: 2022/07/14                                      */
/*    VERSION: 2.1.0                                        */
/*                                                          */
/*    MODIFICATION HISTORY:                                 */
/*    v2.1.0                                                */
/*      - 添加SetReceiveTimeout方法支持接收超时设置         */
/*      - 添加Close方法支持手动关闭socket连接,避免占用套接字  */
/*      - 优化析构函数中的socket关闭逻辑                    */
/*      - 添加-fPIC编译支持以解决PIE链接问题                */
/*                                                          */
/************************************************************/

#ifndef _BLUESOCKET_H_
#define _BLUESOCKET_H_
#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <vector>
#include <stdint.h>

class BlueSocket
{
public:
    BlueSocket();
    virtual ~BlueSocket();

protected:
    int m_iSockfd;
    struct sockaddr_in m_SockAddr;

public:
    bool OpenSocket(const std::string &sIP, const int &iPort);
    bool OpenSocketWithRetry(const std::string &sIP, const int &iPort,
                            const int maxRetries, const int retryDelay);
    bool SetNonBlocking();
    bool BindSocket();
    bool RebindSocket(const int maxRetries, const int retryDelay);
    bool SetReceiveTimeout(int seconds, int microseconds = 0);
    void Close();
    int SendString(const std::string &data);
    int SendBinary(const std::vector<uint8_t> &data);
    int RecvString(std::string &data, const int &iBufferSize);
    int RecvBinary(std::vector<uint8_t> &data, const int &iBufferSize);

protected:
    bool m_bBindFlag;
};

#endif
