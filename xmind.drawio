<mxfile host="65bd71144e">
    <diagram id="_K54HY5sOEbFR5kVoIm5" name="第 1 页">
        <mxGraphModel dx="1628" dy="766" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0" adaptiveColors="auto">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="33" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="35" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="2" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="36" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="2" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="39" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2" target="38" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="42" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="1160" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="110" y="540"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="程序" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="-10" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="3" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="3" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="手动模式（调试用的）" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="240" y="230" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="4" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="4" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="4" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="4" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="自动模式（默认）" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="240" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="5" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="5" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="GF模式" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="240" y="760" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="25" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="6" target="23" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="上升命令" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="150" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="7" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="680" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="下降的命令" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="根据发布的Deisgred——seppd来和moos的配置文件中的最高速度去进行比较，如果小于的话则上升，大于则下降" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="500" y="370" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="自动模式下满足任务结束和期望的速度为则下降" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="500" y="460" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="由GF模式自动切换自动模式" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="540" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="自动模式下同样兼容手动的命令" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="610" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="满足启航且任务结束，并且当前的深度&amp;lt;0.5期望的深度等于0则升起" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="800" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="GF模式下自动下降" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="上升的圈数和下降的圈数可以从moos中修改" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="680" y="210" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="27" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" source="27" target="29" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="应急" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="240" y="920" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="自动模式下的应急是让他升起来" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="890" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="GF模式下的应急是让他下降下去" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="510" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;升降的逻辑导图&lt;/h1&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
                    <mxGeometry x="10" y="10" width="190" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="64" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="38" target="63">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="接收要修改成阻塞式IO" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="240" y="1060" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="43" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.032;entryY=0.607;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="1160" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="1160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="新增加的修改的功能" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="1130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="49" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="46" target="47" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="50" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="46" target="48" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="现在新增的修改如下，修改成读取到第一个CANID为112的报文之后开始用MOOStime计时" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="1160" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="47" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="1210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="最高位置修改成1圈加上280度，也就是640度这是最高位置（完成）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="510" y="1130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="59" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="48" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="1210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="48" value="最低位置为00 00就行（完成）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="510" y="1210" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="51" target="52" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="55" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="51" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="51" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="1210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="工作流程：在接收到112数据帧之后开始计时50s之后不发送控制指令，只进行CANID为112的报文的读取，50-60s之后发送降到底的指令，判断是否卡住）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="740" y="940" width="250" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;（发送指令后，未到0位前的3s内，电流的字节由原来的速度那两个字节替换》阈值或者位置（圈数）不变化&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1060" y="950" width="130" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="发送(停转，保持当前位置)，清零" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1060" y="1030" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="61" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="1210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="60s之后正常工作状态，自动模式上升到顶，若未到顶卡住，发送给电机当前位置减去最高位后的位置控制量，到达后发送清零" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="745" y="1120" width="245" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="62" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="57" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="1200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="57" value="恢复自动模式上升到顶部" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="750" y="1270" width="250" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="修改成类成员函数的Recvform来进行直接调用的" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="510" y="1060" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>