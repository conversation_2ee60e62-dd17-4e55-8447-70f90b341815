# MOOS 升降电机控制系统 uPokeDB 命令参考手册

## 概述

本文档详细说明了 `iElevator_HEU` 模块支持的所有 uPokeDB 命令格式，用于通过 MOOS 数据库控制升降电机系统。

---

## 1. 基本命令格式

### 1.1 命令发送方式
```bash
# 基本语法
uPokeDB VARIABLE_NAME="COMMAND_STRING"

# 示例
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Ascend;"
```

### 1.2 支持的变量类型
- **CONTROL_MSG** - 结构化控制消息（主要命令接口）
- **DESIRED_SPEED** - 期望速度设置
- **Mainframestate** - 主控状态模拟

---

## 2. CONTROL_MSG 命令详细说明

### 2.1 手动控制命令

#### 2.1.1 手动上升
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Ascend;"
```
- **功能**: 手动控制升降机构上升到最大角度位置
- **执行**: 立即执行，上升到配置的 `MAX_ASCEND_ANGLE`

#### 2.1.2 手动下降
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Descend;"
```
- **功能**: 手动控制升降机构下降到最小角度位置
- **执行**: 立即执行，下降到配置的 `MIN_DESCEND_ANGLE`

### 2.2 自动控制命令

#### 2.2.1 启动自动控制
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=Autorise;Enable=yes;"
```
- **功能**: 启动自动升降控制模式
- **行为**: 系统将根据导航状态、任务状态、深度等条件自动执行升降操作

#### 2.2.2 关闭自动控制
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=Autorise;Enable=no;"
```
- **功能**: 关闭自动升降控制模式
- **行为**: 停止所有自动升降逻辑，只响应手动命令

### 2.3 GF模式控制

#### 2.3.1 启动GF模式
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=GF;Enable=yes;"
```
- **功能**: 启动 Ground Floor 模式
- **行为**: 阻止自动升起，保持升降机构在底部位置
//-------------------------------------------------------------------
# 1. 首先启动GF模式
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=GF;Enable=yes;"

# 2. 模拟导航启动（满足"待启航后"条件），这个是启航的命令
uPokeDB CONTROL_MSG="Type=motion;Act=Start;"

# 3. 模拟任务结束（满足"任务结束"条件）
uPokeDB CtrlMission_STATUS="FINISH"

# 4. 模拟深度小于0.5米（满足"深度数据＜0.5米"条件）
uPokeDB Depth=0.3

# 5. 设置期望深度为0（满足无深度任务条件）
uPokeDB DESIRED_DEPTH=0.0

# 6. 自动情况况下的应急升起来，不需要速度的约定，GF模式下的应急不响应
uPokeDB CtrlMission_STATUS="EMERGENCY"
# 7. 设置期望速度为0（满足速度条件）
uPokeDB DESIRED_SPEED=0.0
//-------------------------------------------------------------------
#### 2.3.2 退出GF模式
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=GF;Enable=no;"
```
- **功能**: 退出 GF 模式
- **行为**: 根据当前条件判断是否需要自动升起

#### 2.3.3 停止升起命令（GF模式的别名）
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=stop;Enable=no;"
```
- **功能**: 等同于启动GF模式
- **行为**: 立即停止升起操作，激活GF模式

### 2.4 导航和任务控制

#### 2.4.1 导航启动命令
```bash
uPokeDB CONTROL_MSG="Type=function;Act=NavStart;"
```
- **功能**: 通知系统导航已启动
- **行为**: 触发自动下沉逻辑（如果自动控制已启用）

#### 2.4.2 高速航行启航命令
```bash
uPokeDB CONTROL_MSG="Type=motion;Act=Start;"
```
- **功能**: 高速航行任务启动
- **行为**: 升降机构自动下降到底部位置

### 2.5 系统配置命令

#### 2.5.1 界面清零指令
```bash
uPokeDB CONTROL_MSG="MsgType=control;ACT=function;Type=motion;Reset=yes;"
```
- **功能**: 发送清零指令，重置电机位置
- **行为**: 使用CAN ID 0x7FF发送清零命令，将电机位置重置为零位
- **用途**: 用于校准电机位置或故障恢复

#### 2.5.2 角度控制模式设置
```bash
uPokeDB CONTROL_MSG="MsgType=control;ACT=function;TYPE=motion;Mode=MotorSetAngleMode;"
```
- **功能**: 设置电机为角度控制模式
- **行为**: 命令被忽略（系统默认使用角度控制模式）

---

## 3. 其他控制变量

### 3.1 期望速度设置
```bash
uPokeDB DESIRED_SPEED=50.0
```
- **功能**: 设置期望运行速度
- **单位**: mm/s
- **用途**: 用于高速模式判断和速度控制

### 3.2 主控状态模拟
```bash
uPokeDB Mainframestate="The master has died"
```
- **功能**: 模拟主控失联状态
- **行为**: 触发自动升起保护机制

---

## 4. 常用操作场景

### 4.1 系统启动后手动测试
```bash
# 1. 手动上升测试
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Ascend;"

# 2. 手动下降测试
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Descend;"
```

### 4.2 自动控制模式启动
```bash
# 1. 启动自动控制
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=Autorise;Enable=yes;"

# 2. 模拟导航启动
uPokeDB CONTROL_MSG="Type=function;Act=NavStart;"
```

### 4.3 GF模式操作
```bash
# 1. 启动GF模式（阻止升起）
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=GF;Enable=yes;"

# 2. 退出GF模式（允许升起）
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=GF;Enable=no;"
```

### 4.4 高速模式测试
```bash
# 1. 设置高速（≥2500mm/s）
uPokeDB DESIRED_SPEED=2500.0

# 2. 启动高速航行
uPokeDB CONTROL_MSG="Type=motion;Act=Start;"
```

### 4.5 系统维护操作
```bash
# 1. 电机位置清零（校准）
uPokeDB CONTROL_MSG="MsgType=control;ACT=function;Type=motion;Reset=yes;"

# 2. 手动测试序列（清零后重新测试）
uPokeDB CONTROL_MSG="MsgType=control;ACT=function;Type=motion;Reset=yes;"
sleep 2
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Ascend;"
sleep 5
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=motion;Mode=Descend;"
```