cmake_minimum_required(VERSION 3.2)

# 定义项目名称为iLift_HEU
project(iLift_HEU)

# 开启所有编译警告提示以提高代码质量
add_compile_options(-Wall -Wextra)
# 禁用指向成员函数的转换警告，添加fPIC支持
set(CMAKE_CXX_FLAGS "-Wno-pmf-conversions -fPIC")
# 禁用PIE (Position Independent Executable)
set(CMAKE_EXE_LINKER_FLAGS "-no-pie")

# 指定编译器使用C/C++ 11标准，并设置为必须
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

# 设置头文件搜索路径，包括项目根目录和BlueSocket库的头文件目录
include_directories(${PROJECT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/BlueSocket/include)

# 设置库文件搜索路径，包括BlueSocket库的目录
link_directories(${PROJECT_SOURCE_DIR}/BlueSocket/lib)

# 源文件集合（注释掉的是旧版本的配置）
#set(SRC
#${PROJECT_SOURCE_DIR}/iLift_HEU.cpp
#${PROJECT_SOURCE_DIR}/main.cpp)

# 定义项目源文件列表，包括所有需要编译的源文件
SET(SRC
  iLift_HEU.cpp
  iLift_HEU.h
  main.cpp
  iLift_HEU_Info.h
  iLift_HEU_Info.cpp
  BlueSocket/src/BlueSocket.cpp
)

# 生成可执行文件iLift_HEU，使用上面定义的源文件
add_executable(iLift_HEU ${SRC})

# 链接外部库到生成的可执行文件
# MOOS_LIBRARIES: MOOS框架库
# mbutil: MOOS工具库
# pthread: POSIX线程库，用于创建接收线程
# BlueSocket: 直接编译源文件，不需要链接静态库
target_link_libraries(iLift_HEU
${MOOS_LIBRARIES}
mbutil
pthread)
