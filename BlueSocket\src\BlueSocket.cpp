/************************************************************/
/*    NAME: Bao Jing<PERSON>ang                                   */
/*    ORGN: HEU                                             */
/*    FILE: BlueSocket.cpp                                  */
/*    DATE: 2022/07/14                                      */
/*    VERSION: 2.1.0                                        */
/*                                                          */
/*    MODIFICATION HISTORY:                                 */
/*    v2.1.0 (2024/12/XX) - zhaoqinchao                     */
/*      - 实现SetReceiveTimeout方法，支持socket接收超时设置 */
/*      - 实现Close方法，支持手动关闭socket并重置文件描述符 */
/*      - 优化析构函数，确保socket正确关闭                  */
/*      - 添加详细的错误信息输出和调试信息                  */
/*      - 修复编译时的fPIC相关问题                          */
/*      - 添加OpenSocketWithRetry方法，支持连接失败时自动重试 */
/*      - 添加RebindSocket方法，支持绑定失败时自动重试      */
/*      - 增强错误处理和日志记录，提高网络连接稳定性        */
/*                                                          */
/*    v2.0.0 (2022/07/14) - Bao Jingqiang                   */
/*      - 初始版本实现                                      */
/*      - 基础UDP socket通信功能                            */
/*      - 发送和接收字符串/二进制数据                       */
/************************************************************/

#include "BlueSocket.h"

using namespace std;

BlueSocket::BlueSocket()
{
    // 初始化套接字文件描述符为无效值
    m_iSockfd = -1;
    // 初始化绑定标志
    m_bBindFlag = false;
}

BlueSocket::~BlueSocket()
{
    if (m_iSockfd != -1)
    {
        close(m_iSockfd);
    }
}

bool BlueSocket::OpenSocket(const std::string &sIP, const int &iPort)
{
    m_iSockfd = socket(AF_INET, SOCK_DGRAM, 0);
    memset(&m_SockAddr, 0, sizeof(struct sockaddr_in));
    m_SockAddr.sin_family = AF_INET;
    m_SockAddr.sin_addr.s_addr = inet_addr(sIP.c_str());
    m_SockAddr.sin_port = htons(iPort);

    if (m_iSockfd == -1)
    {
        printf("Cannot OpenSocket: ");
        printf("IP = %s, Port = %d \n", sIP.c_str(), iPort);
        printf("OpenSocket Error = %s \n", strerror(errno));
        return false;
    }
    else
    {
        printf("OpenSocket: ");
        printf("Sockfd = %d, IP = %s, Port = %d \n", m_iSockfd, sIP.c_str(), iPort);
        return true;
    }
}

bool BlueSocket::SetNonBlocking()
{
    int flag = fcntl(m_iSockfd, F_GETFL, 0);
    if (flag < 0)
    {
        printf("Cannot SetNonBlocking: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SetNonBlocking Error = %s \n", strerror(errno));
        return false;
    }
    if (fcntl(m_iSockfd, F_SETFL, flag | O_NONBLOCK) < 0)
    {
        printf("Cannot SetNonBlocking: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SetNonBlocking Error = %s \n", strerror(errno));
        return false;
    }

    printf("SetNonBlocking: ");
    printf("Sockfd = %d \n", m_iSockfd);

    return true;
}

bool BlueSocket::BindSocket()
{
    int ret = bind(m_iSockfd, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));
    if (ret < 0)
    {
        printf("Cannot SocketBind: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketBind Error = %s \n", strerror(errno));
        close(m_iSockfd);
        m_bBindFlag = false;
        return false;
    }
    else
    {
        printf("BindSocket: ");
        printf("Sockfd = %d \n", m_iSockfd);
        m_bBindFlag = true;
        return true;
    }
}
// 接收数据超时设置
bool BlueSocket::SetReceiveTimeout(int seconds, int microseconds)
{
    if (m_iSockfd == -1)
    {
        printf("Cannot SetReceiveTimeout: Socket not opened\n");
        return false;
    }

    struct timeval timeout;
    timeout.tv_sec = seconds;
    timeout.tv_usec = microseconds;

    if (setsockopt(m_iSockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0)
    {
        printf("Cannot SetReceiveTimeout: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SetReceiveTimeout Error = %s \n", strerror(errno));
        return false;
    }

    printf("SetReceiveTimeout: ");
    printf("Sockfd = %d, Timeout = %d.%06d seconds\n", m_iSockfd, seconds, microseconds);
    return true;
}

void BlueSocket::Close()
{
    if (m_iSockfd != -1)
    {
        printf("Closing socket: Sockfd = %d\n", m_iSockfd);
        close(m_iSockfd);
        m_iSockfd = -1;
    }
}

int BlueSocket::SendString(const std::string &data)
{
    ssize_t n = sendto(m_iSockfd, data.c_str(), data.size(), 0, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));

    if (n < 0)
    {
        printf("Cannot SocketSend: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketSend Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::SendBinary(const std::vector<uint8_t> &data)
{
    ssize_t n = sendto(m_iSockfd, data.data(), data.size(), 0, (struct sockaddr *)&m_SockAddr, sizeof(struct sockaddr_in));
//MOOSTrace("BlueSocket::SendBinary size_t = %d \n",n);

    if (n < 0)
    {
        printf("Cannot SocketSendHex: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("SocketSendHex Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::RecvString(std::string &data, const int &iBufferSize)
{
    data.clear();
    data.shrink_to_fit();

    char ReadBuffer[iBufferSize + 1];
    memset(ReadBuffer, 0, sizeof(ReadBuffer));
    socklen_t addrlen = sizeof(struct sockaddr_in);
    int n = recvfrom(m_iSockfd, ReadBuffer, iBufferSize, 0, (struct sockaddr *)&m_SockAddr, &addrlen);

    if (n > 0)
    {
        data = string(ReadBuffer, n);
    }
    else
    {
        printf("Cannot RecvString: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("RecvString Error = %s \n", strerror(errno));
    }

    return n;
}

int BlueSocket::RecvBinary(std::vector<uint8_t> &data, const int &iBufferSize)
{
    data.clear();
    data.shrink_to_fit();

    uint8_t ReadBuffer[iBufferSize];
    memset(ReadBuffer, 0, sizeof(ReadBuffer));
    socklen_t addrlen = sizeof(struct sockaddr_in);
    int n = recvfrom(m_iSockfd, ReadBuffer, iBufferSize, 0, (struct sockaddr *)&m_SockAddr, &addrlen);

    if (n > 0)
    {
        data = vector<uint8_t>(ReadBuffer, ReadBuffer + n);
    }
    else
    {
        printf("Cannot RecvBinary: ");
        printf("Sockfd = %d \n", m_iSockfd);
        printf("RecvBinary Error = %s \n", strerror(errno));
    }

    return n;
}

/**
 * @brief 打开UDP套接字并在失败时重试
 *
 * 如果初始连接尝试失败，该方法将进行多次重试，每次之间有指定的延迟
 *
 * @param sIP          目标IP地址
 * @param iPort        目标端口号
 * @param maxRetries   最大重试次数
 * @param retryDelay   每次重试之间的延迟（秒）
 * @return bool        如果任何尝试成功则返回true，所有尝试都失败则返回false
 */
bool BlueSocket::OpenSocketWithRetry(const std::string &sIP, const int &iPort,
                                    const int maxRetries, const int retryDelay)
{
    // 第一次尝试连接
    bool connected = OpenSocket(sIP, iPort);
    if (connected) {
        printf("[INFO] Socket connection established on first attempt: %s:%d\n", sIP.c_str(), iPort);
        return true;
    }

    // 如果第一次尝试失败，进行重试
    int retryCount = 0;
    while (!connected && retryCount < maxRetries) {
        retryCount++;
        printf("[INFO] Connection failed. Retrying (%d/%d) after %d seconds...\n",
               retryCount, maxRetries, retryDelay);

        // 等待指定的延迟时间
        sleep(retryDelay);

        // 重试连接
        connected = OpenSocket(sIP, iPort);
        if (connected) {
            printf("[INFO] Socket connection established on retry %d: %s:%d\n",
                   retryCount + 1, sIP.c_str(), iPort);
            return true;
        }
    }

    printf("[ERROR] Failed to establish socket connection after %d attempts\n", maxRetries + 1);
    return false;
}

/**
 * @brief 重新绑定套接字
 *
 * 当绑定失败时尝试重新绑定套接字
 *
 * @param maxRetries    最大重试次数
 * @param retryDelay    每次重试之间的延迟（单位：秒）
 * @return bool         成功返回true，失败返回false
 */
bool BlueSocket::RebindSocket(const int maxRetries, const int retryDelay)
{
    // 确保套接字文件描述符有效
    if (m_iSockfd < 0) {
        printf("[ERROR] Invalid socket descriptor for rebinding\n");
        return false;
    }

    // 第一次尝试绑定
    if (bind(m_iSockfd, (sockaddr *)&m_SockAddr, sizeof(sockaddr_in)) == 0) {
        printf("[INFO] Socket rebind successful on first attempt\n");
        m_bBindFlag = true;
        return true;
    }

    // 如果第一次尝试失败，进行重试
    int retryCount = 0;
    while (retryCount < maxRetries) {
        retryCount++;
        printf("[INFO] Rebind failed. Retrying (%d/%d) after %d seconds...\n",
               retryCount, maxRetries, retryDelay);

        // 等待指定的延迟时间
        sleep(retryDelay);

        // 重试绑定
        if (bind(m_iSockfd, (sockaddr *)&m_SockAddr, sizeof(sockaddr_in)) == 0) {
            printf("[INFO] Socket rebind successful on retry %d\n", retryCount);
            m_bBindFlag = true;
            return true;
        }
    }

    printf("[ERROR] Failed to rebind socket after %d attempts\n", maxRetries + 1);
    return false;
}
