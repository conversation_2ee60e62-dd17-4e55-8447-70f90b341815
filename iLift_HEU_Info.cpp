/****************************************************************/
/*   NAME: iLift_HEU_Info                                      */
/*   ORGN: HEU                                                 */
/*   FILE: iLift_HEU_Info.cpp                                  */
/*   DESC: 升降电机上位机通信系统信息实现文件                    */
/*   DATE: 2024                                                */
/****************************************************************/

#include <cstdlib>
#include <iostream>
#include "iLift_HEU_Info.h"
#include "ColorParse.h"
#include "ReleaseInfo.h"

using namespace std;

//----------------------------------------------------------------
// Procedure: showSynopsis

void showSynopsis()
{
  blk("SYNOPSIS:                                                       ");
  blk("------------------------------------                            ");
  blk("  iElevator_HEU 升降电机上位机通信系统                           ");
  blk("  用于通过网络与CAN盒子通信，接收电机状态并发送控制指令          ");
  blk("                                                                ");
  blk("  主要功能：                                                    ");
  blk("  - 角度控制模式（默认）和速度控制模式                          ");
  blk("  - 电机位置精确控制（基于圈数和角度）                          ");
  blk("  - 三种运动状态：上升、下降、静止                              ");
  blk("  - 实时状态监控和异常检测                                      ");
  blk("  - 堵转保护功能                                                ");
  blk("  - 掉电位置保存和恢复                                          ");
  blk("  - 标准MOOS变量接口                                            ");
  blk("                                                                ");
}

//----------------------------------------------------------------
// Procedure: showHelpAndExit

void showHelpAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("Usage: iElevator_HEU file.moos [OPTIONS]                        ");
  blu("=============================================================== ");
  blk("                                                                ");
  showSynopsis();
  blk("                                                                ");
  blk("Options:                                                        ");
  mag("  --alias","=<ProcessName>                                      ");
  blk("      Launch iElevator_HEU with the given process name          ");
  blk("      rather than iElevator_HEU.                                ");
  mag("  --example, -e                                                 ");
  blk("      Display example MOOS configuration block.                 ");
  mag("  --help, -h                                                    ");
  blk("      Display this help message.                                ");
  mag("  --interface, -i                                               ");
  blk("      Display MOOS publications and subscriptions.              ");
  mag("  --version,-v                                                  ");
  blk("      Display the release version of iElevator_HEU.             ");
  blk("                                                                ");
  blk("Note: If argv[2] does not otherwise match a known option,       ");
  blk("      then it will be interpreted as a run alias. This is       ");
  blk("      to support pAntler launching conventions.                 ");
  blk("                                                                ");
  exit(0);
}

//----------------------------------------------------------------
// Procedure: showExampleConfigAndExit

void showExampleConfigAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("iElevator_HEU Example MOOS Configuration                        ");
  blu("=============================================================== ");
  blk("                                                                ");
  blk("ProcessConfig = iElevator_HEU                                   ");
  blk("{                                                               ");
  blk("  AppTick   = 10                                                ");
  blk("  CommsTick = 10                                                ");
  blk("                                                                ");
  blk("  //------------------------------------------                  ");
  blk("  // 网络通信配置                                               ");
  blk("  //------------------------------------------                  ");
  blk("  RecvIP = \"************\"      // 本机IP地址                  ");
  blk("  RecvPort = 8004                // 接收端口                    ");
  blk("  DestIP = \"************\"      // CAN盒子IP地址              ");
  blk("  DestPort = 4004                // CAN盒子端口                ");
  blk("                                                                ");
  blk("  //------------------------------------------                  ");
  blk("  // 电机控制参数                                               ");
  blk("  //------------------------------------------                  ");
  blk("  DEFAULT_SPEED = 50.0           // 默认速度(mm/s或rad/s)       ");
  blk("  MAX_ANGLE = 3600.0             // 最大角度(10圈)              ");
  blk("  MIN_ANGLE = 0.0                // 最小角度                    ");
  blk("  TIMEOUT_SEC = 5.0              // 通信超时时间(秒)            ");
  blk("                                                                ");
  blk("                                                                ");
  blk("}                                                               ");
  blk("                                                                ");
  exit(0);
}


//----------------------------------------------------------------
// Procedure: showInterfaceAndExit

void showInterfaceAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("iElevator_HEU INTERFACE                                         ");
  blu("=============================================================== ");
  blk("                                                                ");
  showSynopsis();
  blk("                                                                ");
  blk("SUBSCRIPTIONS:                                                  ");
  blk("------------------------------------                            ");
  blk("  MOTOR_CMD = string                                            ");
  blk("      电机控制命令。可选值：                                    ");
  blk("      \"UP\" / \"RISE\" / \"RISING\" - 上升到最大位置          ");
  blk("      \"DOWN\" / \"FALL\" / \"FALLING\" - 下降到最小位置       ");
  blk("      \"STOP\" / \"IDLE\" - 停止运动                           ");
  blk("      \"GOTO:angle\" - 移动到指定角度位置                      ");
  blk("                                                                ");
  blk("  MOTOR_TARGET_POSITION = double                                ");
  blk("      目标位置（总角度，单位：度）                              ");
  blk("      范围：MIN_ANGLE 到 MAX_ANGLE                             ");
  blk("                                                                ");
  blk("  MOTOR_ENABLE = bool                                           ");
  blk("      电机使能控制 (true=使能, false=禁用)                     ");
  blk("                                                                ");
  blk("  MOTOR_CLEAR_ERROR = string                                    ");
  blk("      清除错误命令 (\"true\" 或 非零值)                        ");
  blk("                                                                ");
  blk("  MOTOR_SET_MODE = string                                       ");
  blk("      设置控制模式 (\"SPEED\" 或 \"ANGLE\")                    ");
  blk("                                                                ");
  blk("PUBLICATIONS:                                                   ");
  blk("------------------------------------                            ");
  blk("  MOTOR_STATUS = string                                         ");
  blk("      综合状态信息字符串，格式：                                ");
  blk("      \"mode=ANGLE,state=IDLE,speed=0,rounds=5,angle=180,      ");
  blk("       total=1980.0[,error=错误描述]\"                         ");
  blk("                                                                ");
  blk("  MOTOR_POSITION = double                                       ");
  blk("      当前总角度位置（圈数*360 + 当前角度）                    ");
  blk("                                                                ");
  blk("  MOTOR_ANGLE = double                                          ");
  blk("      当前圈内角度（0-359度）                                  ");
  blk("                                                                ");
  blk("  MOTOR_ROUNDS = double                                         ");
  blk("      当前圈数                                                  ");
  blk("                                                                ");
  blk("  MOTOR_SPEED = double                                          ");
  blk("      当前速度（mm/s 或 rad/s）                                ");
  blk("                                                                ");
  blk("  MOTOR_MOTION_STATE = string                                   ");
  blk("      运动状态 (\"RISING\" / \"FALLING\" / \"IDLE\")           ");
  blk("                                                                ");
  blk("  MOTOR_CONTROL_MODE = string                                   ");
  blk("      控制模式 (\"SPEED\" / \"ANGLE\")                         ");
  blk("                                                                ");
  blk("  MOTOR_ERROR = string                                          ");
  blk("      错误描述（无错误时为\"CLEARED\"）                        ");
  blk("                                                                ");
  blk("  MOTOR_ERROR_CODE = double                                     ");
  blk("      错误代码：                                                ");
  blk("      0=无错误, 1=过热, 2=过流, 3=电压过低,                    ");
  blk("      4=编码器错误, 5=堵转, 6=刹车电压过高, 7=DRV错误          ");
  blk("                                                                ");
  blk("  MOTOR_CONNECTED = string                                      ");
  blk("      连接状态 (\"true\" / \"false\")                          ");
  blk("                                                                ");
  blk("  MOTOR_HEARTBEAT = double                                      ");
  blk("      心跳时间戳（MOOSTime）                                   ");
  blk("                                                                ");
  exit(0);
}

//----------------------------------------------------------------
// Procedure: showReleaseInfoAndExit

void showReleaseInfoAndExit()
{
  showReleaseInfo("iElevator_HEU", "gpl");
  exit(0);
}