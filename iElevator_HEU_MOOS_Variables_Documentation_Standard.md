# iElevator_HEU程序变量订阅发布文档
---

## 1. 订阅变量（程序接收的MOOS变量）

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `DESIRED_LIFT_MOTOR_CMD` | string | 电机运动命令 | "TYPE=motion,ACT=MotorUp"=上升至最大位置, "TYPE=motion,ACT=MotorDown"=下降至最小位置, "TYPE=motion,ACT=MotorStop"=停止运动, "TYPE=motion,ACT=MotorGoto"=定位到指定角度 |
| `DESIRED_LIFT_MOTOR_POS` | string | 目标总角度位置 | "TYPE=CONFIG;ID=MOTOR_POS;VALUE=900.0" 单位：度，支持多圈，总角度=圈数×360°+当前角度，作为配置消息发送，这个是configmsg的格式
| `DESIRED_LIFT_MOTOR_EN` | string | 电机使能控制 | "TYPE=motion,ACT=MotorEnable"=使能电机,开启电机，允许电机接收控制命令并运行； "TYPE=motion,ACT=MotorDisable"=禁用电机关闭电机，电机将不响应任何运动命令 |
| `DESIRED_LIFT_MOTOR_CLR` | string | 清除错误标志 | "TYPE=motion,ACT=MotorClearError"=清除所有错误状态 |
| `CONTROL_MSG` | string | 结构化控制消息 | 支持手动控制、自动控制、GF模式等多种控制格式，详见第8节 |
| `DESIRED_SPEED` | double | 期望速度 | 电机期望运行速度值 |
| `Mainframestate` | string | 主控状态监控 | 监控主控连接状态，当值为"The master has died"时触发升降机自动升起 |

---

## 2. 发布变量（程序发布到MOOS数据库的变量）

### 2.1 电机基本状态变量

| 变量名 | 变量类型 | 变量内容 | 单位 | 说明 |
|--------|----------|----------|------|------|
| `LIFT_MOTOR_CONN` | string | 通信连接状态 | - | "true"=连接正常, "false"=连接断开 |
| `LIFT_MOTOR_POS` | double | 当前总角度位置 | 度 | 圈数×360°+当前角度 |
| `LIFT_MOTOR_ANG` | double | 当前圈内角度 | 度 | 范围：0-359° |
| `LIFT_MOTOR_RND` | double | 当前完整圈数 | 圈 | 累计转动圈数 |
| `LIFT_MOTOR_SPD` | double | 当前运动速度 | mm/s | 电机实时运动速度 |

### 2.4 电机控制状态变量

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `LIFT_MOTOR_CTRL` | string | 当前控制模式 | "ANGLE"=角度控制, "SPEED"=速度控制 |
| `LIFT_MOTOR_STAT` | string | 当前运动状态 | "UP"=上升运动, "DOWN"=下降运动, "STOP"=停止状态 |

### 2.3 电机错误诊断变量

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `LIFT_MOTOR_HB` | double | 系统心跳时间戳 | MOOSTime()时间戳，用于监控程序运行状态 |

### 2.3 升降机构状态标志位变量

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `LIFT_MOTOR_AT_TOP` | string | 升到顶部标志 | "true"=已升到顶部位置, "false"=未在顶部位置 |
| `LIFT_MOTOR_AT_BOTTOM` | string | 降到底部标志 | "true"=已降到底部位置, "false"=未在底部位置 |
| `LIFT_MOTOR_IN_MOTION` | string | 在运动标志 | "true"=正在运动中, "false"=静止状态 |
| `LIFT_MOTOR_STUCK_MIDDLE` | string | 卡在中间标志 | "true"=卡在中间位置无法继续运动, "false"=运动正常 |

### 2.5 统一告警变量

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `AUV_WARN_MSG` | string | 电机告警信息 | 格式："ID=MOTOR;CODE=x;NOTE=xxx"<br/>有告警时每3秒发送一次，无告警时不发送 |

### 2.6 综合状态信息变量

| 变量名 | 变量类型 | 变量内容 | 说明 |
|--------|----------|----------|------|
| `LIFT_MOTOR_INFO` | string | 综合状态字符串 | 键值对格式的完整状态信息 |

---

## 3. 告警CODE对照表

**系统告警CODE对照表及示例：**
- CODE=0：通信超时（FATAL级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=Communication Timeout"`
- CODE=1：网络连接失败（FATAL级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Connection Failed"`

**电机告警CODE对照表及示例：**
- CODE=2：电机过热（FATAL级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Motor Overheating"`
- CODE=3：电机过流（FATAL级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Motor Overcurrent"`
- CODE=4：电压过低（WARN级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=4;LEVEL=WARN;NOTE=Motor Undervoltage"`
- CODE=5：电机堵转（FATAL级别）
  - `AUV_WARN_MSG = "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Stall"`

---

## 4. 变量分类说明

### 4.1 按功能分类

| 分类 | 变量数量 | 说明 |
|------|----------|------|
| **订阅变量** | 8个 | 程序接收的电机控制命令和参数设置（包含CONTROL_MSG等新增变量） |
| **基本状态变量** | 5个 | 核心电机状态监控数据 |
| **控制状态变量** | 2个 | 电机控制模式和运动状态 |
| **系统监控变量** | 1个 | 系统心跳监控 |
| **标志位变量** | 4个 | 升降机构关键状态标志位 |
| **统一告警变量** | 1个 | 所有电机告警信息统一使用AUV_WARN_MSG格式 |
| **综合状态变量** | 1个 | 完整状态信息汇总 |

### 4.2 按更新频率分类

| 更新频率 | 变量数量 | 变量类型 |
|----------|----------|----------|
| **按需更新** | 8个 | 所有订阅变量（包含新增的CONTROL_MSG等） |
| **1Hz定时更新** | 6个 | 基本状态、系统监控、综合状态 |
| **变化时更新** | 6个 | 控制模式、运动状态、标志位变量 |
| **错误时更新** | 1个 | 统一告警信息 |

---

## 5. 变量命名规则

### 5.1 前缀规则
- `DESIRED_LIFT_MOTOR_` - 升降电机控制命令变量前缀（订阅变量）
- `LIFT_MOTOR_` - 升降电机状态反馈变量前缀（发布变量）

### 5.2 后缀规则
- `_CMD` - 命令相关
- `_POS` - 位置相关
- `_EN` - 使能控制
- `_CLR` - 清除操作
- `_MODE` - 模式设置
- `_CONN` - 连接状态
- `_ANG` - 角度相关
- `_RND` - 圈数相关
- `_SPD` - 速度相关
- `_CTRL` - 控制相关
- `_STAT` - 状态相关
- `_HB` - 心跳相关
- `_INFO` - 综合信息
- `_AT_TOP` - 顶部位置标志
- `_AT_BOTTOM` - 底部位置标志
- `_IN_MOTION` - 运动状态标志
- `_STUCK_MIDDLE` - 卡在中间标志

**总计变量数量：订阅8个，发布14个**

---

## 6. AUV_WARN_MSG告警格式详细说明

### 6.1 格式规范
```
"ID=MOTOR;CODE=x;LEVEL=FATAL/WARN;NOTE=English Description"
```

### 6.2 字段说明
- **ID字段**：固定为"MOTOR"，标识电机告警
- **CODE字段**：告警编号，使用数字标识不同类型的告警
  - 系统告警：CODE=0~1（通信超时、网络连接失败）
  - 电机告警：CODE=2~5（对应不同告警类型）
- **LEVEL字段**：告警级别，用于应急程序判断响应策略
  - "FATAL"：严重告警，应急程序必须响应
  - "WARN"：警告信息，应急程序可忽略，仅记录日志
- **NOTE字段**：告警描述，使用英文说明告警内容和类型

### 6.3 发送规则
- **有告警时**：每3秒发送一次告警信息
- **无告警时**：不发送告警变量
- **多个告警**：每个告警单独发送一条消息

### 6.4 完整示例
```
// 系统告警示例（FATAL级别 - 应急程序必须响应）
AUV_WARN_MSG = "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=Communication Timeout"
AUV_WARN_MSG = "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Connection Failed"

// 电机严重告警示例（FATAL级别 - 应急程序必须响应）
AUV_WARN_MSG = "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Motor Overheating"
AUV_WARN_MSG = "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Motor Overcurrent"
AUV_WARN_MSG = "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Stall"

// 电机警告示例（WARN级别 - 应急程序可忽略）
AUV_WARN_MSG = "ID=MOTOR;CODE=4;LEVEL=WARN;NOTE=Motor Undervoltage"
```

---

## 7. LIFT_MOTOR_INFO综合状态格式详细说明

### 7.1 格式规范
```
"mode=ANGLE,state=UP,speed=50,rounds=2,angle=180,total=900.0"
```

### 7.2 字段说明
- **mode字段**：控制模式，"ANGLE"或"SPEED"
- **state字段**：运动状态，"UP"、"DOWN"或"STOP"
- **speed字段**：当前速度，数值
- **rounds字段**：当前圈数，整数
- **angle字段**：当前角度，0-359度
- **total字段**：总角度，浮点数

### 7.3 特殊状态值
- `"ERROR"` - 系统错误状态
- `"TIMEOUT"` - 通信超时状态
- `"STALL_PROTECTION"` - 堵转保护状态

---

## 8. CONTROL_MSG结构化控制消息格式详细说明

### 8.1 消息格式概述

CONTROL_MSG采用键值对格式，使用分号分隔不同参数，支持多种控制模式和功能。

### 8.2 手动控制命令

| 功能 | 消息格式 | 说明 |
|------|----------|------|
| 手动控制上升 | `MsgType=control;Act=function;Type=motion;Mode=Ascend;` | 手动控制电机上升运动 |
| 手动控制下降 | `MsgType=control;Act=function;Type=motion;Mode=Descend;` | 手动控制电机下降运动 |

### 8.3 自动控制命令

| 功能 | 消息格式 | 说明 |
|------|----------|------|
| 启动自动控制升降 | `MsgType=control;Act=function;Type=Autorise;Enable=yes;` | 启动自动升降控制模式 |
| 关闭自动控制升降 | `MsgType=control;Act=function;Type=Autorise;Enable=no;` | 关闭自动升降控制模式 |

### 8.4 GF模式控制

| 功能 | 消息格式 | 说明 |
|------|----------|------|
| 订阅GF开启 | `MsgType=control;Act=function;Type=GF;Enable=yes;` | 启动GF（Ground Floor）模式 |
| 退出GF关闭 | `MsgType=control;Act=function;Type=GF;Enable=no;` | 退出GF模式 |

### 8.5 配置命令

| 功能 | 消息格式 | 说明 |
|------|----------|------|
| 总的圈数设置 | `TYPE=CONFIG;ID=MOTOR_POS;VALUE=900.0` | 设置电机总角度位置（调试用） |
| 角度模式设置 | `MsgType=control;ACT=function;TYPE=motion;Mode=MotorSetAngleMode;` | 设置电机为角度控制模式 |

### 8.6 状态监控变量

| 变量名 | 类型 | 说明 |
|--------|------|------|
| `DESIRED_SPEED` | double | 期望速度值，用于控制电机运行速度 |
| `Mainframestate` | string | 主控连接状态监控变量 |

**Mainframestate特殊值处理：**
- 当 `Mainframestate = "The master has died"` 时，升降机自动执行升起操作
- 用于主控失连保护，确保系统安全

### 8.7 消息参数详细说明

#### 8.7.1 通用参数
- **MsgType**: 消息类型，固定为 `"control"`
- **Act**: 动作类型，通常为 `"function"`
- **Type**: 控制类型，支持以下值：
  - `"motion"` - 运动控制
  - `"Autorise"` - 自动控制
  - `"GF"` - GF模式控制
- **Mode**: 运行模式（仅motion类型使用）：
  - `"Ascend"` - 上升
  - `"Descend"` - 下降
- **Enable**: 启用标志（仅Autorise和GF类型使用）：
  - `"yes"` - 启用
  - `"no"` - 禁用

#### 8.7.2 配置参数
- **ID**: 配置项标识符
  - `"MOTOR_POS"` - 电机位置配置
- **VALUE**: 配置值
  - 数值类型，如 `"900.0"` 表示900度
- **ACT**: 动作标识符
  - `"MotorSetAngleMode"` - 设置角度模式

### 8.8 使用示例

```bash
# 手动控制示例
CONTROL_MSG = "MsgType=control;Act=function;Type=motion;Mode=Ascend;"
CONTROL_MSG = "MsgType=control;Act=function;Type=motion;Mode=Descend;"

# 自动控制示例
CONTROL_MSG = "MsgType=control;Act=function;Type=Autorise;Enable=yes;"
CONTROL_MSG = "MsgType=control;Act=function;Type=Autorise;Enable=no;"

# GF模式示例
CONTROL_MSG = "MsgType=control;Act=function;Type=GF;Enable=yes;"
CONTROL_MSG = "MsgType=control;Act=function;Type=GF;Enable=no;"

# 配置示例
CONTROL_MSG = "TYPE=CONFIG;ID=MOTOR_POS;VALUE=900.0"
CONTROL_MSG = "TYPE=motion;ACT=MotorSetAngleMode"

# 状态监控示例
DESIRED_SPEED = 50.0
Mainframestate = "The master has died"
```

### 8.9 消息处理优先级

1. **配置命令** - 最高优先级，立即处理
2. **手动控制命令** - 高优先级，覆盖自动控制
3. **自动控制命令** - 中等优先级
4. **GF模式命令** - 中等优先级
5. **状态监控** - 持续监控，触发式响应

### 8.10 错误处理

- **格式错误**: 忽略格式不正确的消息
- **参数缺失**: 使用默认值或忽略命令
- **冲突命令**: 按优先级处理，后发送的高优先级命令覆盖前面的命令
- **无效值**: 记录错误日志，不执行命令
