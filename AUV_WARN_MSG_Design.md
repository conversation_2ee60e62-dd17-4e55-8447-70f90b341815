# MOOS 升降电机控制系统告警设计文档
//这是系统级的告警，当MOOS配置文件读取失败时触发
AUV_WARN_MSG = "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=MOOS File Read Failed"
//这是系统级的告警，当网络套接字绑定失败时触发
AUV_WARN_MSG = "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Binding Failed"
//这是系统级的告警，当接收线程创建失败时触发
AUV_WARN_MSG = "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Thread Creation Failed"
//这是通信级的告警，当网络通信超时时触发
AUV_WARN_MSG = "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Network Communication Timeout"
```
## 2. 电机硬件告警（基于CAN协议）

```
//这是电机硬件的告警，当电机温度过高时触发（对应CAN协议错误码1）
AUV_WARN_MSG = "ID=MOTOR;CODE=4;LEVEL=FATAL;NOTE=Motor Overheating"
//这是电机硬件的告警，当电机电流过大时触发（对应CAN协议错误码2）
AUV_WARN_MSG = "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Overcurrent"
//这是电机硬件的告警，当电机电压过低时触发（对应CAN协议错误码3）
AUV_WARN_MSG = "ID=MOTOR;CODE=6;LEVEL=WARN;NOTE=Motor Undervoltage"
//这是电机硬件的告警，当编码器出现错误时触发（对应CAN协议错误码4）
AUV_WARN_MSG = "ID=MOTOR;CODE=7;LEVEL=FATAL;NOTE=Encoder Error"
//这是电机硬件的告警，当电机发生堵转时触发（对应CAN协议错误码5）
AUV_WARN_MSG = "ID=MOTOR;CODE=8;LEVEL=FATAL;NOTE=Motor Stall Detected"
//这是电机硬件的告警，当刹车电压异常时触发（对应CAN协议错误码6）
AUV_WARN_MSG = "ID=MOTOR;CODE=9;LEVEL=WARN;NOTE=Brake Voltage Error"
//这是电机硬件的告警，当驱动器发生故障时触发（对应CAN协议错误码7）
AUV_WARN_MSG = "ID=MOTOR;CODE=10;LEVEL=FATAL;NOTE=DRV Driver Error"
```